# 使用AirTest+POCO进行自动化测试

AirTest是一个跨平台的UI自动化测试框架，支持Android、iOS、Windows等平台。POCO是AirTest的一个组件，用于简化UI元素的定位和操作。

## 安装AirTest和POCO
可以通过pip安装AirTest和POCO：
```bash
pip install airtest poco
pip install pocoui
```

## 使用Android设备的规格

- **设备类型**: Mi10S
- **Android版本**: Android 13｜安全更新：2024-03-01
- **OS版本**: *******.TGACNXM
- **处理器**: 高通 骁龙 870
- **内存**: 8GB RAM


## 连接设备
使用AirTest连接设备，可以通过USB或WiFi连接Android设备，或者通过USB连接Android真机。以下是连接Android设备的示例代码：
```python
from airtest.core.api import connect_device
# 连接Android设备
dev = connect_device("Android:///")
```
