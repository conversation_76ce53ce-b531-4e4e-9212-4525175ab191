#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import time
import warnings
from airtest.core.api import start_app
# Try different import approaches for Poco
try:
    from poco.drivers.android.uiautomation import AndroidUiautomationPoco
except ImportError:
    try:
        from poco import Poco
        AndroidUiautomationPoco = Poco
    except ImportError:
        # Fallback: try to get poco from airtest
        try:
            from airtest.core.api import poco
            AndroidUiautomationPoco = poco
        except ImportError:
            print("❌ 无法导入Poco，请安装pocoui: pip install pocoui")
            AndroidUiautomationPoco = None

from app.utils import connect_device_with_fix, alternative_connection_method

# 忽略警告
warnings.filterwarnings("ignore")


class WechatAutomation:
    """微信自动化测试类"""
    
    def __init__(self):
        self.device = None
        self.poco = None
    
    def connect_device(self):
        """连接设备"""
        print("🎯 修复Airtest连接问题")
        print("=" * 40)

        # 主要连接方法
        self.device = connect_device_with_fix()

        # 如果失败，尝试备用方法
        if not self.device:
            print("\n🔄 主连接方法失败，尝试备用连接方法...")
            self.device = alternative_connection_method()

        if not self.device:
            print("\n💥 所有连接方法都失败了!")
            print("\n🛠️ 最后的解决方案：")
            print("1. 安装官方Android SDK Platform Tools:")
            print("   brew install android-platform-tools")
            print("2. 或者下载官方ADB工具替换MacDroid的ADB")
            print("3. 重启Mac和Android设备")
            return False
        
        return True
    
    def initialize_poco(self):
        """初始化Poco"""
        try:
            print("\n🎯 初始化Poco...")
            if AndroidUiautomationPoco is None:
                print("❌ Poco不可用")
                return False
            
            # Try different initialization approaches
            try:
                self.poco = AndroidUiautomationPoco()
            except:
                try:
                    # Try with device parameter
                    self.poco = AndroidUiautomationPoco(device=self.device)
                except:
                    # Try using airtest's poco
                    from airtest.core.api import poco
                    self.poco = poco
            
            return True
        except Exception as e:
            print(f"❌ Poco初始化失败: {e}")
            return False
    
    def ensure_wechat_ready(self):
        """确保微信已启动并准备就绪"""
        try:
            # 检查是否已在微信
            if (self.poco(text="微信").exists() or
                    self.poco(text="通讯录").exists() or
                    self.poco(text="发现").exists()):
                return True

            # 如果不在微信，启动微信
            print("📱 启动微信...")
            start_app("com.tencent.mm")
            time.sleep(5)

            # 等待微信加载
            start_time = time.time()
            while time.time() - start_time < 15:
                if (self.poco(text="微信").exists() or
                        self.poco(text="通讯录").exists()):
                    print("✅ 微信已准备就绪")
                    return True
                time.sleep(1)

            print("❌ 微信启动超时")
            return False

        except Exception as e:
            print(f"❌ 确保微信准备失败: {e}")
            return False
    
    def get_device_info(self):
        """获取设备信息"""
        try:
            from airtest.core.api import device as current_dev
            print("current_dev: ", current_dev())
            brand = current_dev().shell("getprop ro.product.brand").strip()
            model = current_dev().shell("getprop ro.product.model").strip()
            version = current_dev().shell("getprop ro.build.version.release").strip()
            print(f"✅ 设备信息: {brand} {model} Android {version}")
        except Exception as e:
            print(f"⚠️ 获取设备信息失败: {e}")
    
    def test_wechat_interface(self):
        """测试微信界面"""
        print("\n📱 启动微信测试...")
        
        # 使用ensure_wechat_ready方法确保微信准备就绪
        if not self.ensure_wechat_ready():
            print("❌ 微信启动失败")
            return False
        
        # 检查微信界面
        wechat_tabs = ["微信", "通讯录", "发现", "我"]
        found = []

        for tab in wechat_tabs:
            element = self.poco(text=tab)
            if element.exists():
                found.append(tab)
                print(f"✅ 找到: {tab}")
            else:
                print(f"❌ 未找到: {tab}")

        if len(found) >= 2:
            print(f"\n🎉 成功! 找到 {len(found)}/4 个微信界面元素")
            return self.test_wechat_features(found)
        else:
            print(f"\n⚠️ 微信界面识别不完整 ({len(found)}/4)")
            print("可能需要手动启动微信应用")
            return False
    
    def test_wechat_features(self, found_tabs):
        """测试微信功能"""
        try:
            # 测试点击发现
            if "发现" in found_tabs:
                print("测试点击发现...")
                self.poco(text="发现").click()
                time.sleep(3)

                # 查找小程序
                if self.poco(text="小程序").exists():
                    print("✅ 发现小程序入口!")
                    self.poco(text="小程序").click()
                    time.sleep(3)
                    print("✅ 已进入小程序页面")
                else:
                    print("❌ 未找到小程序入口")

            print("\n🎊 测试完成! 自动化环境已就绪")
            print("你现在可以开始编写自动化脚本了:")
            print("  poco(text='某个按钮').click()")
            print("  poco(text='输入框').set_text('内容')")
            return True
            
        except Exception as e:
            print(f"❌ 功能测试失败: {e}")
            return False


def main():
    """主函数"""
    automation = WechatAutomation()
    
    # 连接设备
    if not automation.connect_device():
        return
    
    try:
        # 初始化Poco
        if not automation.initialize_poco():
            return
        
        # 获取设备信息
        automation.get_device_info()
        
        # 测试微信界面
        automation.test_wechat_interface()
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()