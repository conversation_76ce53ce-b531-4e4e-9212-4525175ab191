#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绕过Airtest SDK版本获取问题的解决方案
通过修改环境变量和使用正确的ADB路径来解决
"""

import os
import time
import warnings
import subprocess
from airtest.core.api import connect_device, start_app
from poco.drivers.android.uiautomation import AndroidUiautomationPoco

from src.app.utils import connect_device_with_fix, alternative_connection_method
from src.app.wechat.wechat_init import WechatInit

# 忽略警告
warnings.filterwarnings("ignore")

def connect():
    """主函数"""
    print("🎯 修复Airtest连接问题")
    print("=" * 40)

    # 主要连接方法
    device = connect_device_with_fix()

    # 如果失败，尝试备用方法
    if not device:
        device = alternative_connection_method()

    if not device:
        print("\n💥 所有连接方法都失败了!")
        print("\n🛠️ 最后的解决方案：")
        print("1. 安装官方Android SDK Platform Tools:")
        print("   brew install android-platform-tools")
        print("2. 或者下载官方ADB工具替换MacDroid的ADB")
        print("3. 重启Mac和Android设备")
        return

    try:
        print("\n🎯 初始化Poco...")
        poco = AndroidUiautomationPoco()

        # 获取设备信息
        from airtest.core.api import device as current_dev
        try:

            print("current_dev: ", current_dev())
            brand = current_dev().shell("getprop ro.product.brand").strip()
            model = current_dev().shell("getprop ro.product.model").strip()
            version = current_dev().shell("getprop ro.build.version.release").strip()
            print(f"✅ 设备信息: {brand} {model} Android {version}")
            print("POCO:", poco)
        except:
            pass
        
        return poco
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    poco = connect()

    wechat_init = WechatInit(poco)

    wechat_init.ensure_wechat_ready()


if __name__ == "__main__":
    main()