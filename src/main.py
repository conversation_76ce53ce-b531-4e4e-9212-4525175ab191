#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
绕过Airtest SDK版本获取问题的解决方案
通过修改环境变量和使用正确的ADB路径来解决
"""

import os
import time
import warnings
import subprocess
from airtest.core.api import connect_device, start_app
from poco.drivers.android.uiautomation import AndroidUiautomationPoco

from src.app.wechat.utils import connect_device_with_fix, alternative_connection_method

# 忽略警告
warnings.filterwarnings("ignore")



def main():
    """主函数"""
    print("🎯 修复Airtest连接问题")
    print("=" * 40)

    # 主要连接方法
    device = connect_device_with_fix()

    # 如果失败，尝试备用方法
    if not device:
        device = alternative_connection_method()

    if not device:
        print("\n💥 所有连接方法都失败了!")
        print("\n🛠️ 最后的解决方案：")
        print("1. 安装官方Android SDK Platform Tools:")
        print("   brew install android-platform-tools")
        print("2. 或者下载官方ADB工具替换MacDroid的ADB")
        print("3. 重启Mac和Android设备")
        return

    try:
        print("\n🎯 初始化Poco...")
        poco = AndroidUiautomationPoco()

        # 基础测试
        screen_size = poco.get_screen_size()
        orientation = poco.get_orientation()
        print(f"✅ 屏幕尺寸: {screen_size}")
        print(f"✅ 设备方向: {orientation}")

        # # 获取设备信息
        # from airtest.core.api import device as current_dev
        # try:
        #
        #     print("current_dev: ",current_dev())
        #     brand = current_dev().shell("getprop ro.product.brand").strip()
        #     model = current_dev().shell("getprop ro.product.model").strip()
        #     version = current_dev().shell("getprop ro.build.version.release").strip()
        #     print(f"✅ 设备信息: {brand} {model} Android {version}")
        # except:
        #     pass

        # 微信测试
        print("\n📱 启动微信测试...")
        start_app("com.tencent.mm")
        time.sleep(6)

        # 检查微信界面
        wechat_tabs = ["微信", "通讯录", "发现", "我"]
        found = []

        for tab in wechat_tabs:
            element = poco(text=tab)
            if element.exists():
                found.append(tab)
                print(f"✅ 找到: {tab}")
            else:
                print(f"❌ 未找到: {tab}")

        if len(found) >= 2:
            print(f"\n🎉 成功! 找到 {len(found)}/4 个微信界面元素")

            # 测试点击发现
            if "发现" in found:
                print("测试点击发现...")
                poco(text="发现").click()
                time.sleep(3)

                # 查找小程序
                if poco(text="小程序").exists():
                    print("✅ 发现小程序入口!")
                    poco(text="小程序").click()
                    time.sleep(3)
                    print("✅ 已进入小程序页面")
                else:
                    print("❌ 未找到小程序入口")

            print("\n🎊 测试完成! 自动化环境已就绪")
            print("你现在可以开始编写自动化脚本了:")
            print("  poco(text='某个按钮').click()")
            print("  poco(text='输入框').set_text('内容')")

        else:
            print(f"\n⚠️ 微信界面识别不完整 ({len(found)}/4)")
            print("可能需要手动启动微信应用")

    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()