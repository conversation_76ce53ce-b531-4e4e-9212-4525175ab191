import time

from airtest.core.api import start_app


class WechatInit():
    def __init__(self, poco):
        self.poco = poco


    def ensure_wechat_ready(self):
        """确保微信已启动并准备就绪"""
        try:
            # 检查是否已在微信
            if (self.poco(text="微信").exists() or
                    self.poco(text="通讯录").exists() or
                    self.poco(text="发现").exists()):
                return True

            # 如果不在微信，启动微信
            print("📱 启动微信...")
            start_app("com.tencent.mm")
            time.sleep(5)

            # 等待微信加载
            start_time = time.time()
            while time.time() - start_time < 15:
                if (self.poco(text="微信").exists() or
                        self.poco(text="通讯录").exists()):
                    print("✅ 微信已准备就绪")
                    return True
                time.sleep(1)

            print("❌ 微信启动超时")
            return False

        except Exception as e:
            print(f"❌ 确保微信准备失败: {e}")
            return False