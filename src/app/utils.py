import os
import subprocess
import time

from airtest.core.api import connect_device


def setup_adb_environment():
    """设置正确的ADB环境"""
    # 设置MacDroid的ADB路径,如果没有这个app，后续代码会设置跳过
    adb_path = "/Applications/MacDroid.app/Contents/Resources"

    # 将ADB路径添加到环境变量
    current_path = os.environ.get('PATH', '')
    if adb_path not in current_path:
        os.environ['PATH'] = f"{adb_path}:{current_path}"
        print(f"✅ 已设置ADB路径: {adb_path}")

    # 设置ADB服务器路径（如果需要）
    os.environ['ANDROID_ADB_SERVER_PATH'] = f"{adb_path}/adb"

    return f"{adb_path}/adb"


def verify_adb_consistency(device_id="9230eb1"):
    """验证ADB命令一致性"""
    adb_cmd = setup_adb_environment()

    try:
        # 直接使用完整路径测试
        result = subprocess.run([adb_cmd, '-s', device_id, 'shell', 'getprop', 'ro.build.version.sdk'],
                                capture_output=True, text=True, timeout=10)
        sdk_direct = result.stdout.strip()

        # 使用环境变量中的adb测试
        result = subprocess.run(['adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.sdk'],
                                capture_output=True, text=True, timeout=10)
        sdk_env = result.stdout.strip()

        print(f"直接调用ADB SDK版本: {sdk_direct}")
        print(f"环境变量ADB SDK版本: {sdk_env}")

        return sdk_direct == sdk_env and sdk_direct.isdigit()

    except Exception as e:
        print(f"ADB一致性检查失败: {e}")
        return False


def patch_airtest_adb():
    """尝试修复Airtest的ADB问题"""
    try:
        from airtest.core.android.adb import ADB

        # 保存原始的getprop方法
        original_getprop = ADB.getprop

        def patched_getprop(self, key, default=""):
            """修复的getprop方法"""
            try:
                # 使用我们确认可用的ADB路径
                adb_path = "/Applications/MacDroid.app/Contents/Resources/adb"
                result = subprocess.run([adb_path, '-s', self.serialno, 'shell', 'getprop', key],
                                        capture_output=True, text=True, timeout=10)
                value = result.stdout.strip()

                if key == 'ro.build.version.sdk' and not value:
                    # 如果SDK版本为空，返回默认值
                    print(f"⚠️ SDK版本为空，使用默认值: 28")
                    return "28"  # 使用一个合理的默认SDK版本

                return value if value else default

            except Exception as e:
                print(f"patched_getprop错误: {e}")
                # 降级到原始方法
                try:
                    return original_getprop(self, key, default)
                except:
                    if key == 'ro.build.version.sdk':
                        return "28"  # 默认SDK版本
                    return default

        # 应用补丁
        ADB.getprop = patched_getprop
        print("✅ 已应用ADB补丁")

    except Exception as e:
        print(f"⚠️ 无法应用ADB补丁: {e}")


def connect_device_with_fix():
    """使用修复后的方法连接设备"""
    device_id = "9230eb1"

    print("🚀 开始修复连接...")

    # 1. 设置环境
    setup_adb_environment()

    # 2. 验证ADB一致性
    if not verify_adb_consistency(device_id):
        print("⚠️ ADB一致性问题，应用补丁...")
        patch_airtest_adb()

    # 3. 尝试连接
    connection_attempts = [
        "Android:///",  # 自动连接
        f"Android:///{device_id}",
        f"Android://127.0.0.1:5037/{device_id}",
    ]

    for i, conn_str in enumerate(connection_attempts, 1):
        try:
            print(f"🔌 连接尝试 {i}: {conn_str}")

            device = connect_device(conn_str)

            # 验证连接
            time.sleep(2)
            try:
                from airtest.core.api import device as current_dev
                test_result = current_dev().shell("echo connection_test")
                if "connection_test" in test_result:
                    print("✅ 连接验证成功!")
                    return device
                else:
                    print("❌ 连接验证失败")
                    continue
            except Exception as ve:
                print(f"❌ 连接验证异常: {ve}")
                continue

        except ValueError as e:
            if "invalid literal for int()" in str(e):
                print(f"❌ SDK版本问题依然存在: {e}")

                # 最后的尝试：直接修改airtest源码中的问题
                try:
                    from airtest.core.android.android import Android
                    # 尝试强制设置SDK版本
                    print("🔧 尝试强制修复...")
                    continue
                except:
                    pass
            else:
                print(f"❌ 其他值错误: {e}")
                continue
        except Exception as e:
            print(f"❌ 连接异常: {e}")
            continue

    return None


def alternative_connection_method():
    """备用连接方法 - 使用系统ADB"""
    print("\n🔄 尝试备用连接方法...")

    try:
        # 尝试使用brew安装的ADB（如果有）
        result = subprocess.run(['which', 'adb'], capture_output=True, text=True)
        system_adb = result.stdout.strip()

        if system_adb and system_adb != "/Applications/MacDroid.app/Contents/Resources/adb":
            print(f"发现系统ADB: {system_adb}")

            # 临时修改PATH，让airtest使用系统ADB
            original_path = os.environ.get('PATH', '')
            adb_dir = os.path.dirname(system_adb)
            os.environ['PATH'] = f"{adb_dir}:{original_path}"

            try:
                device = connect_device("Android:///")
                print("✅ 系统ADB连接成功!")
                return device
            except Exception as e:
                print(f"❌ 系统ADB连接失败: {e}")
                # 恢复PATH
                os.environ['PATH'] = original_path

    except Exception as e:
        print(f"备用方法失败: {e}")

    return None